%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 6
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Double
  m_Shader: {fileID: 4800000, guid: 601c929d281a1e34fb4b49a429323690, type: 3}
  m_ShaderKeywords: 
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BumpMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 2800000, guid: d550dc6e8a50113498619a4b61221e04, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex1:
        m_Texture: {fileID: 2800000, guid: d8b2d63d9e8bbfb4f84bc9185cea9f11, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SecondTex:
        m_Texture: {fileID: 2800000, guid: 77fcd144dd09c434f8e74802b9c253ac, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SecondTex1:
        m_Texture: {fileID: 2800000, guid: b1259311b4f518d4286af2b65c34aeed, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ThirdTex:
        m_Texture: {fileID: 2800000, guid: a56e674bcf74dee43bbfaed5f5f33dfa, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ThirdTex1:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Floats:
    - _Brightness: 12.4
    - _Brightness1: 6.3
    - _BumpScale: 1
    - _Cutoff: 0.5
    - _DetailNormalMapScale: 1
    - _DstBlend: 0
    - _FadeAreaHeight: 0.137
    - _FadeAreaHeight1: 0.05
    - _FillLevel: 0.664
    - _FillLevel1: 0.814
    - _GlossMapScale: 1
    - _Glossiness: 0.5
    - _GlossyReflections: 1
    - _HotLineBrightness: 2.81
    - _HotLineBrightness1: 1
    - _HotLineHeight: 0.0296
    - _HotLineHeight1: 0.016
    - _Metallic: 0
    - _Mode: 0
    - _OcclusionStrength: 1
    - _Parallax: 0.02
    - _SmoothnessTextureChannel: 0
    - _SpecularHighlights: 1
    - _SrcBlend: 1
    - _UVSec: 0
    - _VerticalSeparator: 0.5
    - _ZWrite: 1
    m_Colors:
    - _AlphaColor: {r: 1, g: 0, b: 0, a: 0}
    - _AlphaColor1: {r: 0, g: 0, b: 0, a: 0}
    - _Color: {r: 1, g: 1, b: 1, a: 1}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
    - _HotLineColor: {r: 1, g: 1, b: 1, a: 1}
    - _HotLineColor1: {r: 1, g: 1, b: 1, a: 1}
    - _MainColor: {r: 1, g: 0, b: 0, a: 1}
    - _MainColor1: {r: 1, g: 0.5197071, b: 0, a: 1}
    - _Speed1: {r: 1.5, g: -1.5, b: 0, a: 0}
    - _Speed11: {r: 1, g: 0, b: 0, a: 0}
    - _Speed2: {r: -2, g: -1, b: 0, a: 0}
    - _Speed21: {r: 0, g: 2, b: 0, a: 0}
    - _Speed3: {r: 1, g: 0.5, b: 0, a: 0}
    - _Speed31: {r: 0, g: 0, b: 0, a: 0}
