[remap]

importer="texture"
type="CompressedTexture2D"
uid="uid://d4m6bo8fysu12"
path="res://.godot/imported/Hero_Ring_Grey.png-3cd88e8b54a0ab6d8b45d1f26f22b859.ctex"
metadata={
"vram_texture": false
}

[deps]

source_file="res://assets/Gothic/Artworks/Hero_Ring_Grey.png"
dest_files=["res://.godot/imported/Hero_Ring_Grey.png-3cd88e8b54a0ab6d8b45d1f26f22b859.ctex"]

[params]

compress/mode=0
compress/high_quality=false
compress/lossy_quality=0.7
compress/hdr_compression=1
compress/normal_map=0
compress/channel_pack=0
mipmaps/generate=false
mipmaps/limit=-1
roughness/mode=0
roughness/src_normal=""
process/fix_alpha_border=true
process/premult_alpha=false
process/normal_map_invert_y=false
process/hdr_as_srgb=false
process/hdr_clamp_exposure=false
process/size_limit=0
detect_3d/compress_to=1
