[gd_scene load_steps=17 format=3 uid="uid://6ij3qlnr0qr3"]

[ext_resource type="Script" uid="uid://cicxp1a32ye3s" path="res://tests/item_definitions_test.gd" id="2"]
[ext_resource type="Script" uid="uid://hfcic3vuiugx" path="res://tests/quadtree_tests.gd" id="2_bbskb"]
[ext_resource type="Script" uid="uid://dvgjslx6jg2do" path="res://tests/item_count_test.gd" id="2_e28iq"]
[ext_resource type="Script" uid="uid://cu0x1147qss5" path="res://tests/prototree_tests.gd" id="2_ryngl"]
[ext_resource type="Script" uid="uid://j5h4fm8yobsf" path="res://tests/inventory_stacked_tests.gd" id="3"]
[ext_resource type="Script" uid="uid://cnfb5urkuq4a4" path="res://tests/constraint_tests/weight_constraint_tests.gd" id="3_4otuf"]
[ext_resource type="Script" uid="uid://bjlqr5eulcmh4" path="res://tests/inventory_grid_tests.gd" id="5"]
[ext_resource type="Script" uid="uid://d3stwpm3t6jsj" path="res://tests/constraint_tests/grid_constraint_tests.gd" id="5_atslo"]
[ext_resource type="Script" uid="uid://j81v4w6tgyyx" path="res://tests/inventory_tests.gd" id="6"]
[ext_resource type="Script" uid="uid://dqckm1mn4ouku" path="res://tests/constraint_tests/constraint_manager_tests.gd" id="6_xg0v1"]
[ext_resource type="Script" uid="uid://dygrti54hre3h" path="res://tests/constraint_tests/item_count_constraint_tests.gd" id="7_emexd"]
[ext_resource type="Script" uid="uid://cnkvhbfvdiqb7" path="res://tests/inventory_grid_stacked_tests.gd" id="8_btq38"]
[ext_resource type="Script" uid="uid://dkcsx42tgjxq1" path="res://tests/inventory_item_tests.gd" id="10_sgu71"]
[ext_resource type="Script" uid="uid://bf72wpy1pripb" path="res://tests/verification_test.gd" id="14"]
[ext_resource type="Script" uid="uid://dfpomiy7nh8mm" path="res://tests/item_slot_tests.gd" id="14_3213w"]
[ext_resource type="Script" uid="uid://v2hlddmvlkm8" path="res://tests/test_runner.gd" id="15"]

[node name="InventorySystemTests" type="Node"]
script = ExtResource("15")

[node name="QuadtreeTest" type="Node" parent="."]
script = ExtResource("2_bbskb")

[node name="PrototreeTest" type="Node" parent="."]
script = ExtResource("2_ryngl")

[node name="InventorySpaceTest" type="Node" parent="."]
script = ExtResource("2_e28iq")

[node name="WeightConstraintTest" type="Node" parent="."]
script = ExtResource("3_4otuf")

[node name="GridConstraintTest" type="Node" parent="."]
script = ExtResource("5_atslo")

[node name="ItemCountConstraintTests" type="Node" parent="."]
script = ExtResource("7_emexd")

[node name="ConstraintManagerTests" type="Node" parent="."]
script = ExtResource("6_xg0v1")

[node name="VerificationTest" type="Node" parent="."]
script = ExtResource("14")

[node name="ItemDefinitionsTest" type="Node" parent="."]
script = ExtResource("2")

[node name="InventoryTests" type="Node" parent="."]
script = ExtResource("6")

[node name="InventoryItemTests" type="Node" parent="."]
script = ExtResource("10_sgu71")

[node name="InventoryStackedTests" type="Node" parent="."]
script = ExtResource("3")

[node name="InventoryGridTests" type="Node" parent="."]
script = ExtResource("5")

[node name="InventoryGridStackedTests" type="Node" parent="."]
script = ExtResource("8_btq38")

[node name="ItemSlotTests" type="Node" parent="."]
script = ExtResource("14_3213w")
