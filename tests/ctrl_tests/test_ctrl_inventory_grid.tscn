[gd_scene load_steps=5 format=3 uid="uid://i33jyh87gocu"]

[ext_resource type="Script" uid="uid://5hfig5q3x8it" path="res://addons/gloot/core/inventory.gd" id="2_up3cv"]
[ext_resource type="Script" uid="uid://d0pfc3q6sof8y" path="res://addons/gloot/ui/ctrl_inventory_grid.gd" id="3_0nl5x"]
[ext_resource type="Script" uid="uid://b8hbvp4kl88cn" path="res://addons/gloot/core/constraints/grid_constraint.gd" id="3_vieoo"]
[ext_resource type="JSON" path="res://tests/data/protoset_grid.json" id="4_h57xp"]

[node name="Control" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="Inventory" type="Node" parent="."]
script = ExtResource("2_up3cv")
protoset = ExtResource("4_h57xp")
_serialized_format = {
"constraints": {
"res://addons/gloot/core/constraints/grid_constraint.gd": {
"data": {
"item_positions": {
"1": "Vector2i(0, 1)"
},
"size": "Vector2i(5, 5)"
},
"name": &"GridConstraint"
}
},
"items": [{
"protoset": "res://tests/data/protoset_grid.json",
"prototype_id": "item_1x1"
}, {
"protoset": "res://tests/data/protoset_grid.json",
"prototype_id": "item_2x2"
}],
"node_name": "Inventory",
"protoset": "res://tests/data/protoset_grid.json"
}

[node name="GridConstraint" type="Node" parent="Inventory"]
script = ExtResource("3_vieoo")
size = Vector2i(5, 5)

[node name="CtrlInventoryGrid" type="Control" parent="." node_paths=PackedStringArray("inventory")]
custom_minimum_size = Vector2(160, 160)
anchors_preset = 0
offset_right = 160.0
offset_bottom = 160.0
script = ExtResource("3_0nl5x")
inventory = NodePath("../Inventory")
