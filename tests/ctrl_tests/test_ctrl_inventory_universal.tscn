[gd_scene load_steps=5 format=3 uid="uid://bcbnfdh6xdfr6"]

[ext_resource type="Script" uid="uid://dkdk043315r74" path="res://addons/gloot/ui/ctrl_inventory_universal.gd" id="1_vwrjq"]
[ext_resource type="Script" uid="uid://5hfig5q3x8it" path="res://addons/gloot/core/inventory.gd" id="2_dake3"]
[ext_resource type="JSON" path="res://tests/data/protoset_basic.json" id="3_xu41k"]
[ext_resource type="Script" uid="uid://b8hbvp4kl88cn" path="res://addons/gloot/core/constraints/grid_constraint.gd" id="4_qxnty"]

[node name="Control" type="Control"]
layout_mode = 3
anchors_preset = 0

[node name="VBoxContainer" type="VBoxContainer" parent="." node_paths=PackedStringArray("inventory")]
layout_mode = 0
offset_right = 40.0
offset_bottom = 40.0
script = ExtResource("1_vwrjq")
inventory = NodePath("../Inventory")

[node name="Inventory" type="Node" parent="."]
script = ExtResource("2_dake3")
protoset = ExtResource("3_xu41k")
_serialized_format = {
"constraints": {
"res://addons/gloot/core/constraints/grid_constraint.gd": {
"data": {
"item_positions": {
"1": "Vector2i(0, 1)"
},
"size": "Vector2i(5, 5)"
},
"name": &"GridConstraint"
}
},
"items": [{
"protoset": "res://tests/data/protoset_basic.json",
"prototype_id": "item1"
}, {
"protoset": "res://tests/data/protoset_basic.json",
"prototype_id": "item_2"
}],
"node_name": "Inventory",
"protoset": "res://tests/data/protoset_basic.json"
}

[node name="GridConstraint" type="Node" parent="Inventory"]
script = ExtResource("4_qxnty")
size = Vector2i(5, 5)
