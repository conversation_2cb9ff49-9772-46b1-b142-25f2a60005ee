[gd_scene load_steps=9 format=3 uid="uid://b4iegtrw2clvh"]

[ext_resource type="Script" uid="uid://trghhrgo3fax" path="res://addons/gloot/ui/ctrl_item_slot.gd" id="1_64wxb"]
[ext_resource type="Texture2D" uid="uid://bpmdtgysdm2tp" path="res://images/field_background.png" id="2_h16kg"]
[ext_resource type="Texture2D" uid="uid://cdhuw0juxw3up" path="res://images/field_highlighted_background.png" id="3_th5bj"]
[ext_resource type="Script" uid="uid://5hfig5q3x8it" path="res://addons/gloot/core/inventory.gd" id="4_j3nko"]
[ext_resource type="JSON" path="res://tests/data/protoset_basic.json" id="5_46c6m"]
[ext_resource type="Script" uid="uid://c6wangdtwc4m7" path="res://addons/gloot/core/item_slot.gd" id="6_qj5xf"]

[sub_resource type="StyleBoxTexture" id="1"]
texture = ExtResource("2_h16kg")
region_rect = Rect2(0, 0, 32, 32)

[sub_resource type="StyleBoxTexture" id="2"]
texture = ExtResource("3_th5bj")
region_rect = Rect2(0, 0, 32, 32)

[node name="Control" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="CtrlItemSlot" type="Control" parent="." node_paths=PackedStringArray("item_slot")]
layout_mode = 3
anchors_preset = 0
offset_right = 32.0
offset_bottom = 32.0
script = ExtResource("1_64wxb")
item_slot = NodePath("../ItemSlot")
slot_style = SubResource("1")
slot_highlighted_style = SubResource("2")

[node name="Inventory" type="Node" parent="."]
script = ExtResource("4_j3nko")
protoset = ExtResource("5_46c6m")
_serialized_format = {
"items": [{
"protoset": "res://tests/data/protoset_basic.json",
"prototype_id": "item1"
}, {
"protoset": "res://tests/data/protoset_basic.json",
"prototype_id": "item_2"
}],
"node_name": "Inventory",
"protoset": "res://tests/data/protoset_basic.json"
}

[node name="ItemSlot" type="Node" parent="."]
script = ExtResource("6_qj5xf")
protoset = ExtResource("5_46c6m")
