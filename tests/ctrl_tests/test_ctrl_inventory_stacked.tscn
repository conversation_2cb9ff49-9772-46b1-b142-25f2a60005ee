[gd_scene load_steps=7 format=3 uid="uid://dcyi3nd2idb5j"]

[ext_resource type="Script" uid="uid://bikhe74xfo7u4" path="res://addons/gloot/ui/ctrl_inventory_capacity.gd" id="2_6ia13"]
[ext_resource type="Script" uid="uid://5hfig5q3x8it" path="res://addons/gloot/core/inventory.gd" id="2_25j36"]
[ext_resource type="Texture2D" uid="uid://b87v12g1icu3u" path="res://images/item_scroll_blue.png" id="4_ijkns"]
[ext_resource type="JSON" path="res://tests/data/protoset_stacks.json" id="4_uov1j"]
[ext_resource type="Script" uid="uid://bu5mhp7ayfx4j" path="res://addons/gloot/ui/ctrl_inventory.gd" id="5_b1fi0"]
[ext_resource type="Script" uid="uid://ba5wmjtc5l8i3" path="res://addons/gloot/core/constraints/weight_constraint.gd" id="6_tm82y"]

[node name="Control" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="CtrlInventory" type="ItemList" parent="." node_paths=PackedStringArray("inventory")]
layout_mode = 0
offset_right = 352.0
offset_bottom = 240.0
item_count = 2
item_0/text = "minimal_item"
item_1/text = "stackable_item (x10)"
item_1/icon = ExtResource("4_ijkns")
script = ExtResource("5_b1fi0")
inventory = NodePath("../Inventory")

[node name="CtrlInventoryCapacity" type="Control" parent="." node_paths=PackedStringArray("inventory")]
custom_minimum_size = Vector2(0, 23)
anchors_preset = 0
offset_top = 240.0
offset_right = 352.0
offset_bottom = 263.0
script = ExtResource("2_6ia13")
inventory = NodePath("../Inventory")

[node name="Inventory" type="Node" parent="."]
script = ExtResource("2_25j36")
protoset = ExtResource("4_uov1j")
_serialized_format = {
"constraints": {
"res://addons/gloot/core/constraints/weight_constraint.gd": {
"data": {
"capacity": 20.0
},
"name": &"WeightConstraint"
}
},
"items": [{
"protoset": "res://tests/data/protoset_stacks.json",
"prototype_id": "minimal_item"
}, {
"protoset": "res://tests/data/protoset_stacks.json",
"prototype_id": "stackable_item"
}],
"node_name": "Inventory",
"protoset": "res://tests/data/protoset_stacks.json"
}

[node name="WeightConstraint" type="Node" parent="Inventory"]
script = ExtResource("6_tm82y")
capacity = 20.0
