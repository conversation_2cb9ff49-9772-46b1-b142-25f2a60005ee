[gd_scene load_steps=6 format=3 uid="uid://dnmcyf6p4gtgp"]

[ext_resource type="Texture2D" uid="uid://marv3em7xope" path="res://images/item_book_blue.png" id="1_1ie5k"]
[ext_resource type="Script" uid="uid://5hfig5q3x8it" path="res://addons/gloot/core/inventory.gd" id="2"]
[ext_resource type="Texture2D" uid="uid://b87v12g1icu3u" path="res://images/item_scroll_blue.png" id="2_b7vgs"]
[ext_resource type="Script" uid="uid://bu5mhp7ayfx4j" path="res://addons/gloot/ui/ctrl_inventory.gd" id="2_vyj5s"]
[ext_resource type="JSON" path="res://tests/data/protoset_basic.json" id="4_yg38y"]

[node name="Control" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="CtrlInventory" type="ItemList" parent="." node_paths=PackedStringArray("inventory")]
layout_mode = 0
offset_right = 350.0
offset_bottom = 235.0
item_count = 2
item_0/text = "item 1"
item_0/icon = ExtResource("1_1ie5k")
item_1/text = "item 2"
item_1/icon = ExtResource("2_b7vgs")
script = ExtResource("2_vyj5s")
inventory = NodePath("../Inventory")

[node name="Inventory" type="Node" parent="."]
script = ExtResource("2")
protoset = ExtResource("4_yg38y")
_serialized_format = {
"items": [{
"protoset": "res://tests/data/protoset_basic.json",
"prototype_id": "item1"
}, {
"protoset": "res://tests/data/protoset_basic.json",
"prototype_id": "item_2"
}],
"node_name": "Inventory",
"protoset": "res://tests/data/protoset_basic.json"
}
