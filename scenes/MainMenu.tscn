[gd_scene load_steps=8 format=3 uid="uid://bx8vn7qkqxqxq"]

[ext_resource type="Script" uid="uid://dcflxi1ai8tpb" path="res://scripts/MainMenu.gd" id="1_main"]
[ext_resource type="Texture2D" path="res://assets/gothic/Artworks/G_Backgroud_Black.png" id="2_background"]
[ext_resource type="Texture2D" path="res://assets/gothic/Artworks/Window_01.png" id="3_title_panel"]
[ext_resource type="Texture2D" path="res://assets/gothic/Artworks/Button_long.png" id="4_button_normal"]
[ext_resource type="Texture2D" path="res://assets/gothic/Artworks/Button_long2.png" id="5_button_hover"]
[ext_resource type="FontFile" uid="uid://md6m40unc1ik" path="res://assets/UI MMORPG Dark Templar Wenrexa/Font/Berry Rotunda.ttf" id="6_font"]
[ext_resource type="Texture2D" path="res://assets/gothic/Artworks/tracery_01.png" id="7_decoration"]

[node name="MainMenu" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_main")

[node name="Background" type="TextureRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
texture = ExtResource("2_background")
expand_mode = 1
stretch_mode = 6

[node name="TitlePanel" type="NinePatchRect" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -400.0
offset_top = -380.0
offset_right = 400.0
offset_bottom = -180.0
grow_horizontal = 2
grow_vertical = 2
texture = ExtResource("3_title_panel")
patch_margin_left = 40
patch_margin_top = 40
patch_margin_right = 40
patch_margin_bottom = 40

[node name="TitleLabel" type="Label" parent="TitlePanel"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -300.0
offset_top = -50.0
offset_right = 300.0
offset_bottom = 30.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_color = Color(0.85, 0.75, 0.5, 1)
theme_override_colors/font_shadow_color = Color(0, 0, 0, 0.9)
theme_override_constants/shadow_offset_x = 4
theme_override_constants/shadow_offset_y = 4
theme_override_fonts/font = ExtResource("6_font")
theme_override_font_sizes/font_size = 42
text = "PREKLIATE DEDIČSTVO"
horizontal_alignment = 1
vertical_alignment = 1

[node name="TopDecoration" type="TextureRect" parent="."]
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = -200.0
offset_top = 20.0
offset_right = -20.0
offset_bottom = 120.0
grow_horizontal = 0
grow_vertical = 2
texture = ExtResource("7_decoration")
expand_mode = 1
stretch_mode = 4

[node name="BottomDecoration" type="TextureRect" parent="."]
layout_mode = 1
anchors_preset = 3
anchor_left = 1.0
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -200.0
offset_top = -120.0
offset_right = -20.0
offset_bottom = -20.0
grow_horizontal = 0
grow_vertical = 0
texture = ExtResource("7_decoration")
expand_mode = 1
stretch_mode = 4
flip_v = true

[node name="MenuContainer" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -200.0
offset_top = -50.0
offset_right = 200.0
offset_bottom = 300.0
grow_horizontal = 2
grow_vertical = 2

[node name="ButtonContainer" type="VBoxContainer" parent="MenuContainer"]
custom_minimum_size = Vector2(400, 0)
layout_mode = 2

[node name="NovaHraButton" type="TextureButton" parent="MenuContainer/ButtonContainer"]
custom_minimum_size = Vector2(400, 80)
layout_mode = 2
texture_normal = ExtResource("4_button_normal")
texture_pressed = ExtResource("4_button_normal")
texture_hover = ExtResource("5_button_hover")
stretch_mode = 1

[node name="NovaHraLabel" type="Label" parent="MenuContainer/ButtonContainer/NovaHraButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_color = Color(0.85, 0.75, 0.5, 1)
theme_override_colors/font_shadow_color = Color(0, 0, 0, 0.9)
theme_override_constants/shadow_offset_x = 3
theme_override_constants/shadow_offset_y = 3
theme_override_fonts/font = ExtResource("6_font")
theme_override_font_sizes/font_size = 22
text = "Nová Hra"
horizontal_alignment = 1
vertical_alignment = 1

[node name="Spacer1" type="Control" parent="MenuContainer/ButtonContainer"]
custom_minimum_size = Vector2(0, 20)
layout_mode = 2

[node name="PokracovatButton" type="TextureButton" parent="MenuContainer/ButtonContainer"]
custom_minimum_size = Vector2(400, 80)
layout_mode = 2
texture_normal = ExtResource("4_button_normal")
texture_pressed = ExtResource("4_button_normal")
texture_hover = ExtResource("5_button_hover")
stretch_mode = 1

[node name="PokracovatLabel" type="Label" parent="MenuContainer/ButtonContainer/PokracovatButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_color = Color(0.85, 0.75, 0.5, 1)
theme_override_colors/font_shadow_color = Color(0, 0, 0, 0.9)
theme_override_constants/shadow_offset_x = 3
theme_override_constants/shadow_offset_y = 3
theme_override_fonts/font = ExtResource("6_font")
theme_override_font_sizes/font_size = 22
text = "Pokračovať"
horizontal_alignment = 1
vertical_alignment = 1

[node name="Spacer2" type="Control" parent="MenuContainer/ButtonContainer"]
custom_minimum_size = Vector2(0, 20)
layout_mode = 2

[node name="KapitolyButton" type="TextureButton" parent="MenuContainer/ButtonContainer"]
custom_minimum_size = Vector2(400, 80)
layout_mode = 2
texture_normal = ExtResource("4_button_normal")
texture_pressed = ExtResource("4_button_normal")
texture_hover = ExtResource("5_button_hover")
stretch_mode = 1

[node name="KapitolyLabel" type="Label" parent="MenuContainer/ButtonContainer/KapitolyButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_color = Color(0.85, 0.75, 0.5, 1)
theme_override_colors/font_shadow_color = Color(0, 0, 0, 0.9)
theme_override_constants/shadow_offset_x = 3
theme_override_constants/shadow_offset_y = 3
theme_override_fonts/font = ExtResource("6_font")
theme_override_font_sizes/font_size = 22
text = "Kapitoly"
horizontal_alignment = 1
vertical_alignment = 1

[node name="Spacer3" type="Control" parent="MenuContainer/ButtonContainer"]
custom_minimum_size = Vector2(0, 20)
layout_mode = 2

[node name="NastaveniaButton" type="TextureButton" parent="MenuContainer/ButtonContainer"]
custom_minimum_size = Vector2(400, 80)
layout_mode = 2
texture_normal = ExtResource("4_button_normal")
texture_pressed = ExtResource("4_button_normal")
texture_hover = ExtResource("5_button_hover")
stretch_mode = 1

[node name="NastaveniaLabel" type="Label" parent="MenuContainer/ButtonContainer/NastaveniaButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_color = Color(0.85, 0.75, 0.5, 1)
theme_override_colors/font_shadow_color = Color(0, 0, 0, 0.9)
theme_override_constants/shadow_offset_x = 3
theme_override_constants/shadow_offset_y = 3
theme_override_fonts/font = ExtResource("6_font")
theme_override_font_sizes/font_size = 22
text = "Nastavenia"
horizontal_alignment = 1
vertical_alignment = 1

[node name="Spacer4" type="Control" parent="MenuContainer/ButtonContainer"]
custom_minimum_size = Vector2(0, 20)
layout_mode = 2



[node name="OHreButton" type="TextureButton" parent="MenuContainer/ButtonContainer"]
custom_minimum_size = Vector2(400, 80)
layout_mode = 2
texture_normal = ExtResource("4_button_normal")
texture_pressed = ExtResource("4_button_normal")
texture_hover = ExtResource("5_button_hover")
stretch_mode = 1

[node name="OHreLabel" type="Label" parent="MenuContainer/ButtonContainer/OHreButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_color = Color(0.85, 0.75, 0.5, 1)
theme_override_colors/font_shadow_color = Color(0, 0, 0, 0.9)
theme_override_constants/shadow_offset_x = 3
theme_override_constants/shadow_offset_y = 3
theme_override_fonts/font = ExtResource("6_font")
theme_override_font_sizes/font_size = 22
text = "O Hre"
horizontal_alignment = 1
vertical_alignment = 1

[node name="VersionLabel" type="Label" parent="."]
layout_mode = 1
anchors_preset = 3
anchor_left = 1.0
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -120.0
offset_top = -40.0
offset_right = -20.0
offset_bottom = -10.0
grow_horizontal = 0
grow_vertical = 0
theme_override_colors/font_color = Color(0.7, 0.6, 0.4, 0.8)
theme_override_fonts/font = ExtResource("6_font")
theme_override_font_sizes/font_size = 14
text = "verzia 1.0.0"
horizontal_alignment = 2
vertical_alignment = 2
