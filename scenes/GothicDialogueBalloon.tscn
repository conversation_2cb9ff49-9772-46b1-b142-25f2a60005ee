[gd_scene load_steps=6 format=3 uid="uid://bqxvn8qkqxqxq"]

[ext_resource type="Script" path="res://scripts/GothicDialogueBalloon.gd" id="1_script"]
[ext_resource type="PackedScene" uid="uid://cplo5pblja3l5" path="res://addons/dialogue_manager/dialogue_label.tscn" id="2_dialogue_label"]
[ext_resource type="Texture2D" path="res://assets/gothic/Artworks/Dialoge_frame.png" id="3_dialogue_frame"]
[ext_resource type="Texture2D" path="res://assets/gothic/Artworks/Button_medium.png" id="4_button_normal"]
[ext_resource type="Texture2D" path="res://assets/gothic/Artworks/Button_medium2.png" id="5_button_hover"]

[node name="GothicDialogueBalloon" type="CanvasLayer"]
script = ExtResource("1_script")

[node name="Balloon" type="Control" parent="."]
unique_name_in_owner = true
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="DialoguePanel" type="NinePatchRect" parent="Balloon"]
layout_mode = 1
anchors_preset = 12
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 20.0
offset_top = -320.0
offset_right = -20.0
offset_bottom = -20.0
grow_horizontal = 2
grow_vertical = 0
texture = ExtResource("3_dialogue_frame")
patch_margin_left = 40
patch_margin_top = 40
patch_margin_right = 40
patch_margin_bottom = 40

[node name="MainContainer" type="VBoxContainer" parent="Balloon/DialoguePanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 30.0
offset_top = 30.0
offset_right = -30.0
offset_bottom = -30.0
grow_horizontal = 2
grow_vertical = 2

[node name="CharacterContainer" type="HBoxContainer" parent="Balloon/DialoguePanel/MainContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 50)

[node name="CharacterLabel" type="RichTextLabel" parent="Balloon/DialoguePanel/MainContainer/CharacterContainer"]
unique_name_in_owner = true
layout_mode = 2
size_flags_horizontal = 3
custom_minimum_size = Vector2(0, 50)
bbcode_enabled = true
text = "Character Name"
fit_content = true
scroll_active = false

[node name="ContentContainer" type="HBoxContainer" parent="Balloon/DialoguePanel/MainContainer"]
layout_mode = 2
size_flags_vertical = 3

[node name="PortraitContainer" type="MarginContainer" parent="Balloon/DialoguePanel/MainContainer/ContentContainer"]
layout_mode = 2
custom_minimum_size = Vector2(120, 0)

[node name="CharacterPortrait" type="TextureRect" parent="Balloon/DialoguePanel/MainContainer/ContentContainer/PortraitContainer"]
unique_name_in_owner = true
layout_mode = 2
size_flags_horizontal = 4
size_flags_vertical = 4
custom_minimum_size = Vector2(100, 100)
expand_mode = 1
stretch_mode = 5

[node name="DialogueContainer" type="VBoxContainer" parent="Balloon/DialoguePanel/MainContainer/ContentContainer"]
layout_mode = 2
size_flags_horizontal = 3

[node name="DialogueLabel" parent="Balloon/DialoguePanel/MainContainer/ContentContainer/DialogueContainer" instance=ExtResource("2_dialogue_label")]
unique_name_in_owner = true
layout_mode = 2
size_flags_vertical = 3
custom_minimum_size = Vector2(0, 120)

[node name="ButtonContainer" type="HBoxContainer" parent="Balloon/DialoguePanel/MainContainer"]
layout_mode = 2
size_flags_horizontal = 4
custom_minimum_size = Vector2(0, 60)

[node name="ContinueButton" type="TextureButton" parent="Balloon/DialoguePanel/MainContainer/ButtonContainer"]
unique_name_in_owner = true
layout_mode = 2
custom_minimum_size = Vector2(150, 50)
texture_normal = ExtResource("4_button_normal")
texture_hover = ExtResource("5_button_hover")
stretch_mode = 1

[node name="ContinueLabel" type="Label" parent="Balloon/DialoguePanel/MainContainer/ButtonContainer/ContinueButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
text = "Pokračovať"
horizontal_alignment = 1
vertical_alignment = 1

[node name="ResponsesMenu" type="MarginContainer" parent="Balloon"]
unique_name_in_owner = true
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = -300.0
offset_top = -350.0
offset_right = 300.0
offset_bottom = -50.0
grow_horizontal = 2
grow_vertical = 0

[node name="ResponsesContainer" type="VBoxContainer" parent="Balloon/ResponsesMenu"]
layout_mode = 2
