[gd_scene load_steps=8 format=3 uid="uid://bkqm8n9qkrxrxr"]

[ext_resource type="Script" path="res://scripts/AboutGameNew.gd" id="1_script"]
[ext_resource type="Texture2D" path="res://assets/gothic/Artworks/G_Backgroud_Black.png" id="2_bg"]
[ext_resource type="Texture2D" path="res://assets/gothic/Artworks/Window_01.png" id="3_main_panel"]
[ext_resource type="Texture2D" path="res://assets/gothic/Artworks/pop-up_menu.png" id="4_content_panel"]
[ext_resource type="Texture2D" path="res://assets/gothic/Artworks/Button_medium.png" id="5_button"]
[ext_resource type="Texture2D" path="res://assets/gothic/Artworks/Button_medium2.png" id="6_button_hover"]

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_1"]
texture = ExtResource("4_content_panel")
texture_margin_left = 20.0
texture_margin_top = 20.0
texture_margin_right = 20.0
texture_margin_bottom = 20.0

[node name="AboutGame" type="Control"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
script = ExtResource("1_script")

[node name="Background" type="TextureRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
texture = ExtResource("2_bg")
expand_mode = 1
stretch_mode = 6

[node name="MainPanel" type="NinePatchRect" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -350.0
offset_top = -450.0
offset_right = 350.0
offset_bottom = 450.0
texture = ExtResource("3_main_panel")
patch_margin_left = 40
patch_margin_top = 40
patch_margin_right = 40
patch_margin_bottom = 40

[node name="ContentContainer" type="VBoxContainer" parent="MainPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 30.0
offset_top = 30.0
offset_right = -30.0
offset_bottom = -30.0

[node name="TitleContainer" type="CenterContainer" parent="MainPanel/ContentContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 80)

[node name="TitleLabel" type="Label" parent="MainPanel/ContentContainer/TitleContainer"]
layout_mode = 2
text = "O HRE"
horizontal_alignment = 1
vertical_alignment = 1

[node name="Spacer1" type="Control" parent="MainPanel/ContentContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 20)

[node name="ContentPanel" type="NinePatchRect" parent="MainPanel/ContentContainer"]
layout_mode = 2
size_flags_vertical = 3
texture = ExtResource("4_content_panel")
patch_margin_left = 20
patch_margin_top = 20
patch_margin_right = 20
patch_margin_bottom = 20

[node name="ScrollContainer" type="ScrollContainer" parent="MainPanel/ContentContainer/ContentPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 25.0
offset_top = 25.0
offset_right = -25.0
offset_bottom = -25.0
horizontal_scroll_mode = 0
vertical_scroll_mode = 3
scroll_deadzone = 0

[node name="ContentVBox" type="VBoxContainer" parent="MainPanel/ContentContainer/ContentPanel/ScrollContainer"]
layout_mode = 2
size_flags_horizontal = 3

[node name="GameTitleContainer" type="CenterContainer" parent="MainPanel/ContentContainer/ContentPanel/ScrollContainer/ContentVBox"]
layout_mode = 2
custom_minimum_size = Vector2(0, 60)

[node name="GameTitle" type="Label" parent="MainPanel/ContentContainer/ContentPanel/ScrollContainer/ContentVBox/GameTitleContainer"]
layout_mode = 2
text = "VAN HELSING: PREKLIATE DEDIČSTVO"
horizontal_alignment = 1
vertical_alignment = 1

[node name="Spacer2" type="Control" parent="MainPanel/ContentContainer/ContentPanel/ScrollContainer/ContentVBox"]
layout_mode = 2
custom_minimum_size = Vector2(0, 30)

[node name="Description" type="RichTextLabel" parent="MainPanel/ContentContainer/ContentPanel/ScrollContainer/ContentVBox"]
layout_mode = 2
size_flags_vertical = 3
bbcode_enabled = true
text = "[center][b]Gotická hra inšpirovaná príbehmi Van Helsinga[/b][/center]"
fit_content = true
add_theme_stylebox_override("normal", SubResource("StyleBoxTexture_1"))

[node name="Spacer3" type="Control" parent="MainPanel/ContentContainer/ContentPanel/ScrollContainer/ContentVBox"]
layout_mode = 2
custom_minimum_size = Vector2(0, 30)

[node name="PolicyButtonsContainer" type="VBoxContainer" parent="MainPanel/ContentContainer/ContentPanel/ScrollContainer/ContentVBox"]
layout_mode = 2

[node name="PrivacyPolicyButton" type="TextureButton" parent="MainPanel/ContentContainer/ContentPanel/ScrollContainer/ContentVBox/PolicyButtonsContainer"]
layout_mode = 2
custom_minimum_size = Vector2(200, 40)
texture_normal = ExtResource("5_button")
texture_hover = ExtResource("6_button_hover")
stretch_mode = 1

[node name="PrivacyPolicyLabel" type="Label" parent="MainPanel/ContentContainer/ContentPanel/ScrollContainer/ContentVBox/PolicyButtonsContainer/PrivacyPolicyButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
text = "OCHRANA OSOBNÝCH ÚDAJOV"
horizontal_alignment = 1
vertical_alignment = 1

[node name="ButtonSpacer" type="Control" parent="MainPanel/ContentContainer/ContentPanel/ScrollContainer/ContentVBox/PolicyButtonsContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 15)

[node name="TermsOfServiceButton" type="TextureButton" parent="MainPanel/ContentContainer/ContentPanel/ScrollContainer/ContentVBox/PolicyButtonsContainer"]
layout_mode = 2
custom_minimum_size = Vector2(200, 40)
texture_normal = ExtResource("5_button")
texture_hover = ExtResource("6_button_hover")
stretch_mode = 1

[node name="TermsOfServiceLabel" type="Label" parent="MainPanel/ContentContainer/ContentPanel/ScrollContainer/ContentVBox/PolicyButtonsContainer/TermsOfServiceButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
text = "PODMIENKY POUŽÍVANIA"
horizontal_alignment = 1
vertical_alignment = 1

[node name="Spacer4" type="Control" parent="MainPanel/ContentContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 30)

[node name="ButtonContainer" type="CenterContainer" parent="MainPanel/ContentContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 80)

[node name="BackButton" type="TextureButton" parent="MainPanel/ContentContainer/ButtonContainer"]
layout_mode = 2
custom_minimum_size = Vector2(120, 40)
texture_normal = ExtResource("5_button")
texture_hover = ExtResource("6_button_hover")
stretch_mode = 1

[node name="BackLabel" type="Label" parent="MainPanel/ContentContainer/ButtonContainer/BackButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
text = "SPÄŤ"
horizontal_alignment = 1
vertical_alignment = 1
