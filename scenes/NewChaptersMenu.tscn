[gd_scene load_steps=20 format=3 uid="uid://cxm8n9qkrxrxr"]

[ext_resource type="Script" path="res://scripts/NewChaptersMenu.gd" id="1_script"]
[ext_resource type="Texture2D" path="res://assets/gothic/Artworks/G_Backgroud_Black.png" id="2_bg"]
[ext_resource type="Texture2D" path="res://assets/gothic/Artworks/Window_01.png" id="3_main_panel"]
[ext_resource type="Texture2D" path="res://assets/gothic/Artworks/pop-up_menu.png" id="4_list_panel"]
[ext_resource type="Texture2D" path="res://assets/gothic/Artworks/Bar_Button_long.png" id="5_line_current"]
[ext_resource type="Texture2D" path="res://assets/gothic/Artworks/Bar_Button_lil_yellow.png" id="6_line_hover"]

[ext_resource type="Texture2D" path="res://assets/gothic/Artworks/Button_medium.png" id="8_button"]
[ext_resource type="Texture2D" path="res://assets/gothic/Artworks/Button_medium2.png" id="9_button_hover"]
[ext_resource type="Texture2D" path="res://assets/Kapitoly_VISUALS/Kapitola_1.png" id="10_ch1"]
[ext_resource type="Texture2D" path="res://assets/Kapitoly_VISUALS/Kapitola_2.png" id="11_ch2"]
[ext_resource type="Texture2D" path="res://assets/Kapitoly_VISUALS/Kapitola_3.png" id="12_ch3"]
[ext_resource type="Texture2D" path="res://assets/Kapitoly_VISUALS/Kapitola_4.png" id="13_ch4"]
[ext_resource type="Texture2D" path="res://assets/Kapitoly_VISUALS/Kapitola_5.png" id="14_ch5"]
[ext_resource type="Texture2D" path="res://assets/Kapitoly_VISUALS/Kapitola_6.png" id="15_ch6"]
[ext_resource type="Texture2D" path="res://assets/Kapitoly_VISUALS/Kapitola_7.png" id="16_ch7"]
[ext_resource type="Texture2D" path="res://assets/Kapitoly_VISUALS/Kapitola_7.png" id="17_epilog"]
[ext_resource type="Texture2D" path="res://assets/gothic/Artworks/DescriptionBar.png" id="18_textfield"]
[ext_resource type="Theme" path="res://themes/DarkTemplarTheme.tres" id="19_theme"]
[ext_resource type="Texture2D" path="res://assets/gothic/Artworks/G_arrow.png" id="20_arrow_normal"]
[ext_resource type="Texture2D" path="res://assets/gothic/Artworks/Button_arrow.png" id="21_arrow_hover"]

[node name="ChaptersMenu" type="Control"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
theme = ExtResource("19_theme")
script = ExtResource("1_script")

[node name="Background" type="TextureRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
texture = ExtResource("2_bg")
expand_mode = 1
stretch_mode = 6

[node name="MainPanel" type="NinePatchRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 10.0
offset_top = 20.0
offset_right = -10.0
offset_bottom = -20.0
texture = ExtResource("3_main_panel")
patch_margin_left = 30
patch_margin_top = 30
patch_margin_right = 30
patch_margin_bottom = 30

[node name="MainContainer" type="VBoxContainer" parent="MainPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 20.0
offset_top = 20.0
offset_right = -20.0
offset_bottom = -80.0

[node name="CarouselContainer" type="HBoxContainer" parent="MainPanel/MainContainer"]
layout_mode = 2
alignment = 1
theme_override_constants/separation = 30

[node name="PrevButton" type="TextureButton" parent="MainPanel/MainContainer/CarouselContainer"]
layout_mode = 2
custom_minimum_size = Vector2(60, 60)
texture_normal = ExtResource("20_arrow_normal")
texture_hover = ExtResource("21_arrow_hover")
stretch_mode = 4
flip_h = true

[node name="ChapterIndicator" type="Label" parent="MainPanel/MainContainer/CarouselContainer"]
layout_mode = 2
custom_minimum_size = Vector2(150, 60)
text = "1 / 7"
horizontal_alignment = 1
vertical_alignment = 1

[node name="NextButton" type="TextureButton" parent="MainPanel/MainContainer/CarouselContainer"]
layout_mode = 2
custom_minimum_size = Vector2(60, 60)
texture_normal = ExtResource("20_arrow_normal")
texture_hover = ExtResource("21_arrow_hover")
stretch_mode = 4

[node name="Spacer1" type="Control" parent="MainPanel/MainContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 20)

[node name="ChapterImageContainer" type="Control" parent="MainPanel/MainContainer"]
layout_mode = 2
size_flags_vertical = 3
size_flags_stretch_ratio = 1.5

[node name="CenterContainer" type="CenterContainer" parent="MainPanel/MainContainer/ChapterImageContainer"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0

[node name="ChapterImage" type="TextureRect" parent="MainPanel/MainContainer/ChapterImageContainer/CenterContainer"]
layout_mode = 2
custom_minimum_size = Vector2(320, 240)
texture = ExtResource("10_ch1")
expand_mode = 1
stretch_mode = 6

[node name="Spacer2" type="Control" parent="MainPanel/MainContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 20)

[node name="ChapterInfoContainer" type="Control" parent="MainPanel/MainContainer"]
layout_mode = 2
size_flags_vertical = 3
size_flags_stretch_ratio = 1.0

[node name="InfoPanel" type="NinePatchRect" parent="MainPanel/MainContainer/ChapterInfoContainer"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
texture = ExtResource("18_textfield")
patch_margin_left = 15
patch_margin_top = 15
patch_margin_right = 15
patch_margin_bottom = 15

[node name="InfoContent" type="VBoxContainer" parent="MainPanel/MainContainer/ChapterInfoContainer/InfoPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 30.0
offset_top = 45.0
offset_right = -30.0
offset_bottom = -30.0
theme_override_constants/separation = 20

[node name="ChapterTitle" type="Label" parent="MainPanel/MainContainer/ChapterInfoContainer/InfoPanel/InfoContent"]
layout_mode = 2
text = "Kapitola 1 - Cesta na zámok"
horizontal_alignment = 1

[node name="Spacer3" type="Control" parent="MainPanel/MainContainer/ChapterInfoContainer/InfoPanel/InfoContent"]
layout_mode = 2
custom_minimum_size = Vector2(0, 15)

[node name="ChapterDescription" type="RichTextLabel" parent="MainPanel/MainContainer/ChapterInfoContainer/InfoPanel/InfoContent"]
layout_mode = 2
size_flags_vertical = 3
bbcode_enabled = true
text = "Marec 1894. Tvoje kolesá sa krútia po blatistej ceste naprieč karpatskými lesmi, kam ťa zavolal posledný telegram od mentora. V tejto úvodnej kapitole začínaš v kolísajúcom kočiari."
fit_content = true
scroll_active = false
theme_override_constants/line_separation = 12



[node name="ButtonsContainer" type="HBoxContainer" parent="MainPanel"]
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = -120.0
offset_top = -80.0
offset_right = 120.0
offset_bottom = -40.0

[node name="PlayButton" type="TextureButton" parent="MainPanel/ButtonsContainer"]
layout_mode = 2
custom_minimum_size = Vector2(100, 40)
texture_normal = ExtResource("8_button")
texture_hover = ExtResource("9_button_hover")
stretch_mode = 0

[node name="PlayLabel" type="Label" parent="MainPanel/ButtonsContainer/PlayButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
text = "HRAŤ"
horizontal_alignment = 1
vertical_alignment = 1

[node name="Spacer" type="Control" parent="MainPanel/ButtonsContainer"]
layout_mode = 2
custom_minimum_size = Vector2(20, 0)

[node name="BackButton" type="TextureButton" parent="MainPanel/ButtonsContainer"]
layout_mode = 2
custom_minimum_size = Vector2(100, 40)
texture_normal = ExtResource("8_button")
texture_hover = ExtResource("9_button_hover")
stretch_mode = 0

[node name="BackLabel" type="Label" parent="MainPanel/ButtonsContainer/BackButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
text = "SPÄŤ"
horizontal_alignment = 1
vertical_alignment = 1
