[gd_scene load_steps=8 format=3 uid="uid://dx0voaqkqxqxq"]

[ext_resource type="Script" path="res://scripts/SettingsMenuNew.gd" id="1_script"]
[ext_resource type="Texture2D" path="res://assets/gothic/Artworks/G_Backgroud_Black.png" id="2_bg"]
[ext_resource type="Texture2D" path="res://assets/gothic/Artworks/Window_01.png" id="3_main_panel"]
[ext_resource type="Texture2D" path="res://assets/gothic/Artworks/pop-up_menu.png" id="4_settings_panel"]
[ext_resource type="Texture2D" path="res://assets/gothic/Artworks/DescriptionBar.png" id="5_slider_panel"]
[ext_resource type="Texture2D" path="res://assets/gothic/Artworks/XP_bar_long.png" id="6_slider_bg"]
[ext_resource type="Texture2D" path="res://assets/gothic/Artworks/Button_medium.png" id="9_button"]
[ext_resource type="Texture2D" path="res://assets/gothic/Artworks/Button_medium2.png" id="10_button_hover"]

[node name="SettingsMenu" type="Control"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
script = ExtResource("1_script")

[node name="Background" type="TextureRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
texture = ExtResource("2_bg")
expand_mode = 1
stretch_mode = 6

[node name="MainPanel" type="NinePatchRect" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -350.0
offset_top = -450.0
offset_right = 350.0
offset_bottom = 450.0
texture = ExtResource("3_main_panel")
patch_margin_left = 40
patch_margin_top = 40
patch_margin_right = 40
patch_margin_bottom = 40

[node name="ContentContainer" type="VBoxContainer" parent="MainPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 30.0
offset_top = 30.0
offset_right = -30.0
offset_bottom = -30.0

[node name="TitleContainer" type="CenterContainer" parent="MainPanel/ContentContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 80)

[node name="TitleLabel" type="Label" parent="MainPanel/ContentContainer/TitleContainer"]
layout_mode = 2
text = "NASTAVENIA"
horizontal_alignment = 1
vertical_alignment = 1

[node name="Spacer1" type="Control" parent="MainPanel/ContentContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 20)

[node name="SettingsPanel" type="NinePatchRect" parent="MainPanel/ContentContainer"]
layout_mode = 2
size_flags_vertical = 3
texture = ExtResource("4_settings_panel")
patch_margin_left = 20
patch_margin_top = 20
patch_margin_right = 20
patch_margin_bottom = 20

[node name="ScrollContainer" type="ScrollContainer" parent="MainPanel/ContentContainer/SettingsPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 25.0
offset_top = 25.0
offset_right = -25.0
offset_bottom = -25.0
horizontal_scroll_mode = 0
vertical_scroll_mode = 3
scroll_deadzone = 0

[node name="SettingsVBox" type="VBoxContainer" parent="MainPanel/ContentContainer/SettingsPanel/ScrollContainer"]
layout_mode = 2
size_flags_horizontal = 3

[node name="MasterVolumeContainer" type="VBoxContainer" parent="MainPanel/ContentContainer/SettingsPanel/ScrollContainer/SettingsVBox"]
layout_mode = 2
custom_minimum_size = Vector2(0, 100)

[node name="VolumeLabel" type="Label" parent="MainPanel/ContentContainer/SettingsPanel/ScrollContainer/SettingsVBox/MasterVolumeContainer"]
layout_mode = 2
text = "Hlavná hlasitosť: 100%"
horizontal_alignment = 1

[node name="SliderContainer" type="CenterContainer" parent="MainPanel/ContentContainer/SettingsPanel/ScrollContainer/SettingsVBox/MasterVolumeContainer"]
layout_mode = 2
size_flags_vertical = 3

[node name="VolumeSlider" type="HSlider" parent="MainPanel/ContentContainer/SettingsPanel/ScrollContainer/SettingsVBox/MasterVolumeContainer/SliderContainer"]
layout_mode = 2
custom_minimum_size = Vector2(400, 40)

[node name="Spacer1" type="Control" parent="MainPanel/ContentContainer/SettingsPanel/ScrollContainer/SettingsVBox"]
layout_mode = 2
custom_minimum_size = Vector2(0, 30)

[node name="MusicVolumeContainer" type="VBoxContainer" parent="MainPanel/ContentContainer/SettingsPanel/ScrollContainer/SettingsVBox"]
layout_mode = 2
custom_minimum_size = Vector2(0, 100)

[node name="VolumeLabel" type="Label" parent="MainPanel/ContentContainer/SettingsPanel/ScrollContainer/SettingsVBox/MusicVolumeContainer"]
layout_mode = 2
text = "Hudba: 100%"
horizontal_alignment = 1

[node name="SliderContainer" type="CenterContainer" parent="MainPanel/ContentContainer/SettingsPanel/ScrollContainer/SettingsVBox/MusicVolumeContainer"]
layout_mode = 2
size_flags_vertical = 3

[node name="VolumeSlider" type="HSlider" parent="MainPanel/ContentContainer/SettingsPanel/ScrollContainer/SettingsVBox/MusicVolumeContainer/SliderContainer"]
layout_mode = 2
custom_minimum_size = Vector2(400, 40)

[node name="Spacer2" type="Control" parent="MainPanel/ContentContainer/SettingsPanel/ScrollContainer/SettingsVBox"]
layout_mode = 2
custom_minimum_size = Vector2(0, 30)

[node name="SFXVolumeContainer" type="VBoxContainer" parent="MainPanel/ContentContainer/SettingsPanel/ScrollContainer/SettingsVBox"]
layout_mode = 2
custom_minimum_size = Vector2(0, 100)

[node name="VolumeLabel" type="Label" parent="MainPanel/ContentContainer/SettingsPanel/ScrollContainer/SettingsVBox/SFXVolumeContainer"]
layout_mode = 2
text = "Zvukové efekty: 100%"
horizontal_alignment = 1

[node name="SliderContainer" type="CenterContainer" parent="MainPanel/ContentContainer/SettingsPanel/ScrollContainer/SettingsVBox/SFXVolumeContainer"]
layout_mode = 2
size_flags_vertical = 3

[node name="VolumeSlider" type="HSlider" parent="MainPanel/ContentContainer/SettingsPanel/ScrollContainer/SettingsVBox/SFXVolumeContainer/SliderContainer"]
layout_mode = 2
custom_minimum_size = Vector2(400, 40)



[node name="Spacer4" type="Control" parent="MainPanel/ContentContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 30)

[node name="ButtonContainer" type="CenterContainer" parent="MainPanel/ContentContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 80)

[node name="BackButton" type="TextureButton" parent="MainPanel/ContentContainer/ButtonContainer"]
layout_mode = 2
custom_minimum_size = Vector2(120, 40)
texture_normal = ExtResource("9_button")
texture_hover = ExtResource("10_button_hover")
stretch_mode = 1

[node name="BackLabel" type="Label" parent="MainPanel/ContentContainer/ButtonContainer/BackButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
text = "SPÄŤ"
horizontal_alignment = 1
vertical_alignment = 1
