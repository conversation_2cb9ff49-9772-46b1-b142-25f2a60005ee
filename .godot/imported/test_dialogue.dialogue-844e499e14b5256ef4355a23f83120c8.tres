[gd_resource type="Resource" script_class="DialogueResource" load_steps=2 format=3]

[ext_resource type="Script" uid="uid://dbs4435dsf3ry" path="res://addons/dialogue_manager/dialogue_resource.gd" id="1_5htoh"]

[resource]
script = ExtResource("1_5htoh")
using_states = PackedStringArray()
titles = {
"test_start": "2"
}
character_names = PackedStringArray("Rozprávač", "Van Helsing")
first_title = "0"
lines = {
"0": {
&"id": "0",
&"next_id": "2",
&"type": "title"
},
"1": {
&"id": "1",
&"next_id": "2",
&"type": ""
},
"2": {
&"character": "Rozpr<PERSON><PERSON><PERSON>",
&"id": "2",
&"next_id": "3",
&"text": "Toto je test nového dialogue systému.",
&"type": "dialogue"
},
"3": {
&"character": "<PERSON>",
&"id": "3",
&"next_id": "4",
&"text": "Fungu<PERSON> to správne?",
&"type": "dialogue"
},
"4": {
&"character": "Rozprávač",
&"id": "4",
&"next_id": "6",
&"text": "Áno, Gothic UI vyzerá skvele!",
&"type": "dialogue"
},
"5": {
&"id": "5",
&"next_id": "6",
&"type": ""
},
"6": {
&"id": "6",
&"is_snippet": false,
&"next_id": "end",
&"next_id_after": "",
&"type": "goto"
},
"7": {
&"id": "7",
&"next_id": "8",
&"type": ""
},
"8": {
&"id": "8",
&"is_snippet": false,
&"next_id": "end",
&"next_id_after": "",
&"type": "goto"
}
}
raw_text = "~ test_start

Rozprávač: Toto je test nového dialogue systému.
Van Helsing: Funguje to správne?
Rozprávač: Áno, Gothic UI vyzerá skvele!

=> END
"
metadata/dialogue_manager_version = "3.7.1"
