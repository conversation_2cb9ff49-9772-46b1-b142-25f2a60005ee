class_name GothicDialogueBalloon extends <PERSON>vas<PERSON>ayer
## Gothic dialogue balloon for Van Helsing game with mobile portrait optimization

## The action to use for advancing the dialogue
@export var next_action: StringName = &"ui_accept"

## The action to use to skip typing the dialogue
@export var skip_action: StringName = &"ui_cancel"

## The dialogue resource
var resource: DialogueResource

## Temporary game states
var temporary_game_states: Array = []

## See if we are waiting for the player
var is_waiting_for_input: bool = false

## See if we are running a long mutation and should hide the balloon
var will_hide_balloon: bool = false

## A dictionary to store any ephemeral variables
var locals: Dictionary = {}

var _locale: String = TranslationServer.get_locale()

## The current line
var dialogue_line: DialogueLine:
	set(value):
		if value:
			dialogue_line = value
			apply_dialogue_line()
		else:
			# The dialogue has finished so close the balloon
			queue_free()
	get:
		return dialogue_line

## A cooldown timer for delaying the balloon hide when encountering a mutation.
var mutation_cooldown: Timer = Timer.new()

## UI References
@onready var balloon: Control = %Balloon
@onready var character_label: RichTextLabel = %CharacterLabel
@onready var dialogue_label: DialogueLabel = %DialogueLabel
@onready var character_portrait: TextureRect = %CharacterPortrait
@onready var continue_button: TextureButton = %ContinueButton
@onready var responses_menu: Control = %ResponsesMenu

## Portrait textures
var portraits: Dictionary = {
	"Rozprávač": preload("res://assets/Avatary/Rozpravac.png"),
	"Van Helsing": preload("res://assets/Avatary/VanHelsing.png"),
	"Kočiar": preload("res://assets/Avatary/Kociar.png"),
	"Strážca": preload("res://assets/Avatary/Strazca.png"),
	"Knihovník": preload("res://assets/Avatary/Knihovnik.png"),
	"Alchymista": preload("res://assets/Avatary/Alchymista.png"),
	"Mních": preload("res://assets/Avatary/Mních.png")
}

func _ready():
	# Setup mobile layout
	setup_mobile_layout()
	
	# Setup mutation cooldown
	add_child(mutation_cooldown)
	mutation_cooldown.wait_time = 0.1
	mutation_cooldown.timeout.connect(_on_mutation_cooldown_timeout)
	
	# Connect signals
	if continue_button:
		continue_button.pressed.connect(_on_continue_button_pressed)
	
	# Setup fonts and styling
	setup_gothic_styling()

func setup_mobile_layout():
	"""Optimize layout for mobile portrait (720x1280)"""
	var viewport_size = get_viewport().get_visible_rect().size
	
	# Adjust dialogue panel size for mobile
	var dialogue_panel = balloon.get_node("DialoguePanel")
	if viewport_size.x <= 800:  # Mobile
		dialogue_panel.offset_left = 10
		dialogue_panel.offset_right = -10
		dialogue_panel.offset_top = -280
		dialogue_panel.offset_bottom = -10
	
	# Adjust portrait size for mobile
	if character_portrait:
		character_portrait.custom_minimum_size = Vector2(80, 80)

func setup_gothic_styling():
	"""Apply Gothic fonts and colors"""
	if character_label:
		character_label.add_theme_color_override("default_color", Color(0.85, 0.75, 0.5, 1))
		character_label.add_theme_font_size_override("normal_font_size", 22)
	
	if dialogue_label:
		dialogue_label.add_theme_color_override("default_color", Color(0.9, 0.85, 0.75, 1))
		dialogue_label.add_theme_font_size_override("normal_font_size", 18)

func start(dialogue_resource: DialogueResource, title: String = "", extra_game_states: Array = []):
	"""Start the dialogue"""
	resource = dialogue_resource
	temporary_game_states = extra_game_states
	
	# Show the balloon with animation
	show_balloon()
	
	# Get the first line
	dialogue_line = await resource.get_next_dialogue_line(title, temporary_game_states)

func show_balloon():
	"""Show balloon with fade animation"""
	modulate = Color.TRANSPARENT
	var tween = create_tween()
	tween.tween_property(self, "modulate", Color.WHITE, 0.3)

func hide_balloon():
	"""Hide balloon with fade animation"""
	var tween = create_tween()
	tween.tween_property(self, "modulate", Color.TRANSPARENT, 0.3)
	await tween.finished
	queue_free()

func apply_dialogue_line():
	"""Apply the current dialogue line to the UI"""
	if not dialogue_line:
		return
	
	# Set character name
	if character_label:
		character_label.text = "[b]" + dialogue_line.character + "[/b]"
	
	# Set character portrait
	show_character_portrait(dialogue_line.character)
	
	# Set dialogue text
	if dialogue_label:
		dialogue_label.dialogue_line = dialogue_line
	
	# Show/hide continue button based on responses
	if dialogue_line.responses.size() > 0:
		continue_button.visible = false
		show_responses()
	else:
		continue_button.visible = true
		responses_menu.visible = false
		is_waiting_for_input = true

func show_character_portrait(character_name: String):
	"""Show character portrait"""
	if not character_portrait:
		return
	
	if character_name in portraits:
		character_portrait.texture = portraits[character_name]
		character_portrait.visible = true
	else:
		character_portrait.visible = false

func show_responses():
	"""Show response options"""
	# Clear existing responses
	var responses_container = responses_menu.get_node("ResponsesContainer")
	for child in responses_container.get_children():
		child.queue_free()
	
	# Add new responses
	for response in dialogue_line.responses:
		var response_button = create_response_button(response)
		responses_container.add_child(response_button)
	
	responses_menu.visible = true

func create_response_button(response) -> TextureButton:
	"""Create a response button with Gothic styling"""
	var button = TextureButton.new()
	button.texture_normal = preload("res://assets/gothic/Artworks/Button_long.png")
	button.texture_hover = preload("res://assets/gothic/Artworks/Button_long2.png")
	button.stretch_mode = TextureButton.STRETCH_SCALE
	button.custom_minimum_size = Vector2(500, 50)
	
	var label = Label.new()
	label.text = response.text
	label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	label.vertical_alignment = VERTICAL_ALIGNMENT_CENTER
	label.add_theme_color_override("font_color", Color(0.85, 0.75, 0.5, 1))
	label.add_theme_font_size_override("font_size", 16)
	
	button.add_child(label)
	label.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	
	button.pressed.connect(_on_response_selected.bind(response))
	
	return button

func _on_continue_button_pressed():
	"""Handle continue button press"""
	if not is_waiting_for_input:
		return
	
	is_waiting_for_input = false
	dialogue_line = await resource.get_next_dialogue_line(dialogue_line.next_id, temporary_game_states)

func _on_response_selected(response):
	"""Handle response selection"""
	dialogue_line = await resource.get_next_dialogue_line(response.next_id, temporary_game_states)

func _on_mutation_cooldown_timeout():
	"""Handle mutation cooldown"""
	if will_hide_balloon:
		hide_balloon()

func _input(event):
	"""Handle input events"""
	if not visible:
		return
	
	if event.is_action_pressed(next_action) and is_waiting_for_input:
		_on_continue_button_pressed()
		get_viewport().set_input_as_handled()
	elif event.is_action_pressed(skip_action) and dialogue_label.is_typing:
		dialogue_label.skip_typing()
		get_viewport().set_input_as_handled()
