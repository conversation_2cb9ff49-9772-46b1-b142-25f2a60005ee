[gd_scene load_steps=18 format=3 uid="uid://dkfn8qojbtud4"]

[ext_resource type="Script" uid="uid://d0pfc3q6sof8y" path="res://addons/gloot/ui/ctrl_inventory_grid.gd" id="3"]
[ext_resource type="Script" uid="uid://c5ro8fq1haxrt" path="res://examples/inventory_grid_transfer.gd" id="4"]
[ext_resource type="Script" uid="uid://trghhrgo3fax" path="res://addons/gloot/ui/ctrl_item_slot.gd" id="6"]
[ext_resource type="JSON" path="res://tests/data/protoset_grid.json" id="8_kriwd"]
[ext_resource type="Texture2D" uid="uid://bpmdtgysdm2tp" path="res://images/field_background.png" id="9"]
[ext_resource type="Script" uid="uid://5hfig5q3x8it" path="res://addons/gloot/core/inventory.gd" id="9_xnylc"]
[ext_resource type="Texture2D" uid="uid://cdhuw0juxw3up" path="res://images/field_highlighted_background.png" id="10"]
[ext_resource type="Script" uid="uid://b8hbvp4kl88cn" path="res://addons/gloot/core/constraints/grid_constraint.gd" id="10_4n750"]
[ext_resource type="Script" uid="uid://c6wangdtwc4m7" path="res://addons/gloot/core/item_slot.gd" id="10_mbp7e"]

[sub_resource type="StyleBoxTexture" id="1"]
texture = ExtResource("9")
region_rect = Rect2(0, 0, 32, 32)

[sub_resource type="StyleBoxTexture" id="2"]
texture = ExtResource("10")
region_rect = Rect2(0, 0, 32, 32)

[sub_resource type="StyleBoxFlat" id="5"]
draw_center = false
border_width_left = 2
border_width_top = 2
border_width_right = 2
border_width_bottom = 2
border_color = Color(1, 0.92549, 0.152941, 1)

[sub_resource type="StyleBoxTexture" id="3"]
texture = ExtResource("9")
region_rect = Rect2(0, 0, 32, 32)

[sub_resource type="StyleBoxTexture" id="4"]
texture = ExtResource("10")
region_rect = Rect2(0, 0, 32, 32)

[sub_resource type="StyleBoxFlat" id="6"]
draw_center = false
border_width_left = 2
border_width_top = 2
border_width_right = 2
border_width_bottom = 2
border_color = Color(1, 0.92549, 0.152941, 1)

[sub_resource type="StyleBoxTexture" id="7"]
texture = ExtResource("9")
region_rect = Rect2(0, 0, 32, 32)

[sub_resource type="StyleBoxTexture" id="8"]
texture = ExtResource("10")
region_rect = Rect2(0, 0, 32, 32)

[node name="InventoryGridTransfer" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("4")

[node name="VBoxContainer" type="VBoxContainer" parent="."]
layout_mode = 0
offset_right = 40.0
offset_bottom = 40.0

[node name="HBoxContainer" type="HBoxContainer" parent="VBoxContainer"]
layout_mode = 2
size_flags_horizontal = 0
size_flags_vertical = 0

[node name="VBoxContainer" type="VBoxContainer" parent="VBoxContainer/HBoxContainer"]
layout_mode = 2

[node name="PanelContainer" type="PanelContainer" parent="VBoxContainer/HBoxContainer/VBoxContainer"]
layout_mode = 2

[node name="CtrlInventoryGridLeft" type="Control" parent="VBoxContainer/HBoxContainer/VBoxContainer/PanelContainer" node_paths=PackedStringArray("inventory")]
unique_name_in_owner = true
texture_filter = 1
custom_minimum_size = Vector2(160, 160)
layout_mode = 2
script = ExtResource("3")
inventory = NodePath("../../../../../InventoryLeft")
field_style = SubResource("1")
field_highlighted_style = SubResource("2")
selection_style = SubResource("5")

[node name="BtnSortLeft" type="Button" parent="VBoxContainer/HBoxContainer/VBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
size_flags_horizontal = 3
text = "Sort"

[node name="VBoxContainer2" type="VBoxContainer" parent="VBoxContainer/HBoxContainer"]
layout_mode = 2

[node name="PanelContainer2" type="PanelContainer" parent="VBoxContainer/HBoxContainer/VBoxContainer2"]
layout_mode = 2

[node name="CtrlInventoryGridRight" type="Control" parent="VBoxContainer/HBoxContainer/VBoxContainer2/PanelContainer2" node_paths=PackedStringArray("inventory")]
unique_name_in_owner = true
texture_filter = 1
custom_minimum_size = Vector2(160, 160)
layout_mode = 2
script = ExtResource("3")
inventory = NodePath("../../../../../InventoryRight")
field_style = SubResource("3")
field_highlighted_style = SubResource("4")
selection_style = SubResource("6")

[node name="BtnSortRight" type="Button" parent="VBoxContainer/HBoxContainer/VBoxContainer2"]
unique_name_in_owner = true
layout_mode = 2
size_flags_horizontal = 3
text = "Sort"

[node name="VBoxContainer3" type="VBoxContainer" parent="VBoxContainer/HBoxContainer"]
layout_mode = 2

[node name="Label" type="Label" parent="VBoxContainer/HBoxContainer/VBoxContainer3"]
layout_mode = 2
text = "Item Slot:"

[node name="PanelContainer" type="PanelContainer" parent="VBoxContainer/HBoxContainer/VBoxContainer3"]
layout_mode = 2
size_flags_horizontal = 4

[node name="CtrlItemSlot" type="Control" parent="VBoxContainer/HBoxContainer/VBoxContainer3/PanelContainer" node_paths=PackedStringArray("item_slot")]
custom_minimum_size = Vector2(32, 32)
layout_mode = 2
script = ExtResource("6")
item_slot = NodePath("../../../../../ItemSlot")
icon_stretch_mode = 5
slot_style = SubResource("7")
slot_highlighted_style = SubResource("8")

[node name="BtnUnequip" type="Button" parent="VBoxContainer/HBoxContainer/VBoxContainer3"]
unique_name_in_owner = true
layout_mode = 2
text = "Unequip"

[node name="Label" type="Label" parent="VBoxContainer"]
layout_mode = 2
text = "Drag and drop items to transfer them from one inventory to the other.
Press the 'Sort' buttons to sort the inventories.
Drag items from the right inventory onto the item slot to equip it.
Use the 'Unequip' button to unequip it."

[node name="LblInfo" type="Label" parent="."]
unique_name_in_owner = true
visible = false
layout_mode = 0
offset_right = 40.0
offset_bottom = 14.0

[node name="ItemSlot" type="Node" parent="."]
unique_name_in_owner = true
script = ExtResource("10_mbp7e")
protoset = ExtResource("8_kriwd")

[node name="InventoryLeft" type="Node" parent="."]
unique_name_in_owner = true
script = ExtResource("9_xnylc")
protoset = ExtResource("8_kriwd")
_serialized_format = {
"constraints": {
"res://addons/gloot/core/constraints/grid_constraint.gd": {
"data": {
"item_positions": {
"1": "Vector2i(0, 1)"
},
"size": "Vector2i(5, 5)"
},
"name": &"GridConstraint"
}
},
"items": [{
"protoset": "res://tests/data/protoset_grid.json",
"prototype_id": "item_1x1"
}, {
"protoset": "res://tests/data/protoset_grid.json",
"prototype_id": "item_2x2"
}],
"node_name": "InventoryLeft",
"protoset": "res://tests/data/protoset_grid.json"
}

[node name="GridConstraint" type="Node" parent="InventoryLeft"]
script = ExtResource("10_4n750")
size = Vector2i(5, 5)

[node name="InventoryRight" type="Node" parent="."]
unique_name_in_owner = true
script = ExtResource("9_xnylc")
protoset = ExtResource("8_kriwd")
_serialized_format = {
"constraints": {
"res://addons/gloot/core/constraints/grid_constraint.gd": {
"data": {
"item_positions": {
"1": "Vector2i(0, 1)"
},
"size": "Vector2i(5, 5)"
},
"name": &"GridConstraint"
}
},
"items": [{
"protoset": "res://tests/data/protoset_grid.json",
"prototype_id": "item_1x1"
}, {
"protoset": "res://tests/data/protoset_grid.json",
"prototype_id": "item_2x2"
}],
"node_name": "InventoryRight",
"protoset": "res://tests/data/protoset_grid.json"
}

[node name="GridConstraint" type="Node" parent="InventoryRight"]
script = ExtResource("10_4n750")
size = Vector2i(5, 5)
